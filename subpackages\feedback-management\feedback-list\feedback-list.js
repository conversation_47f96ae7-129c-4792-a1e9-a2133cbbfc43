// pages/feedback-list/feedback-list.js
Page({
  data: {
    feedbacks: [],
    loading: true,
    taskId: null,
    taskInfo: null, // 任务单信息

    // 分类数据
    groupedFeedbacks: [], // 按工程和任务单分类的反馈单数据
    expandedProjects: {}, // 展开的工程
    expandedTasks: {}, // 展开的任务单

    // 搜索相关
    searchKeyword: '',
    filteredGroupedFeedbacks: [], // 过滤后的分类数据

    // 统计信息
    totalFeedbackCount: 0, // 总反馈数量

    currentCompany: null,
    userInfo: null,

    // 页面状态管理
    isFirstLoad: true, // 标记是否为首次加载

    // 分页相关
    hasMore: true, // 是否还有更多数据
    currentPage: 1, // 当前页码
    pageSize: 50, // 每页大小
    isLoadingMore: false, // 是否正在加载更多
  },

  onLoad(options) {
    this.checkLogin();

    if (options.taskId) {
      this.setData({ taskId: options.taskId });
      this.loadFeedbacks(options.taskId);
    } else {
      // 如果没有指定任务单，显示当前公司的所有现场信息反馈记录
      // 首次加载时重置展开状态
      this.loadAllFeedbacks(true);
    }
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.taskId) {
      this.loadFeedbacks(this.data.taskId);
    } else {
      this.loadAllFeedbacks();
    }

    // 标记不再是首次加载
    if (this.data.isFirstLoad) {
      this.setData({ isFirstLoad: false });
    }
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = wx.getStorageSync("userInfo");
    const currentCompany = wx.getStorageSync("currentCompany");

    if (!userInfo) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    if (!currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/index/index",
        });
      }, 2000);
      return;
    }

    this.setData({
      userInfo,
      currentCompany,
    });
  },

  // 加载指定任务单的现场信息反馈记录
  async loadFeedbacks(taskId) {
    try {
      this.setData({ loading: true });
      const app = getApp();

      // 同时获取任务单信息和反馈记录
      const [taskRes, feedbackRes] = await Promise.all([
        app.request({
          url: `/api/tasks/${taskId}`,
          method: "GET",
        }),
        app.request({
          url: `/api/feedbacks/task/${taskId}`,
          method: "GET",
        })
      ]);

      if (feedbackRes.data.success) {
        const feedbacks = feedbackRes.data.data.map((item) => ({
          ...item,
          feedback_time_text: this.formatDateTime(item.feedback_time),
        }));

        // 设置任务单信息
        let taskInfo = null;
        if (taskRes.data.success && taskRes.data.data.task) {
          const task = taskRes.data.data.task;
          taskInfo = {
            task_number: task.task_number,
            part_name: task.part_name,
            strength_grade: task.strength_grade,
            scheduled_time: this.formatDateTime(task.scheduled_time) || this.formatDateTime(task.created_at),
            scheduled_time_label: task.scheduled_time ? "计划时间" : "创建时间",
            project_name: task.project_name,
            construction_unit: task.construction_unit,
          };
        }

        this.setData({
          feedbacks,
          taskInfo,
          loading: false,
        });

        // 更新页面标题
        if (taskInfo) {
          wx.setNavigationBarTitle({
            title: `${taskInfo.task_number} - 现场信息反馈记录`,
          });
        }
      } else {
        wx.showToast({
          title: feedbackRes.data.message || "加载失败",
          icon: "none",
        });
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error("加载现场信息反馈记录失败:", error);
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
      this.setData({ loading: false });
    }
  },

  // 加载当前用户的所有现场信息反馈记录
  // resetExpandState: 是否重置展开状态，默认为false（保持当前状态）
  async loadAllFeedbacks(resetExpandState = false) {
    try {
      this.setData({ loading: true });
      const app = getApp();

      // 优先使用分组API（显示工程下的所有任务单）
      let res;
      try {
        console.log("尝试使用分组API加载反馈单数据");
        res = await app.request({
          url: "/api/feedbacks/user/grouped",
          method: "GET",
        });

        if (res.data.success) {
          console.log("分组API返回的数据:", res.data.data);
          const groupedFeedbacks = res.data.data.map((project) => {
            console.log("工程数据:", project);
            console.log("工程下的任务单:", project.tasks);

            return {
              ...project,
              tasks: project.tasks.map((task) => {
                console.log("任务单数据:", task);
                console.log("任务单ID:", task.id);

                // 确保任务单有id字段
                const taskWithId = {
                  ...task,
                  id: task.id || task.task_number, // 如果没有id，使用task_number
                  feedbacks: task.feedbacks.map((feedback) => ({
                    ...feedback,
                    feedback_time_text: this.formatDateTime(feedback.feedback_time),
                  })),
                };

                console.log("处理后的任务单:", taskWithId);
                return taskWithId;
              }),
            };
          });

          // 计算总反馈数量
          const totalFeedbacks = this.calculateTotalFeedbackCount(groupedFeedbacks);

          // 准备要更新的数据
          const updateData = {
            groupedFeedbacks: groupedFeedbacks,
            filteredGroupedFeedbacks: groupedFeedbacks,
            totalFeedbackCount: totalFeedbacks,
            feedbacks: [],
            taskInfo: null,
            loading: false,
          };

          // 在首次加载或明确要求重置时重置展开状态
          if (this.data.isFirstLoad || resetExpandState) {
            updateData.expandedProjects = {};
            updateData.expandedTasks = {};
          }

          this.setData(updateData);

          wx.setNavigationBarTitle({
            title: `我的现场信息反馈记录 (${totalFeedbacks})`,
          });
          return; // 成功加载，直接返回
        }
      } catch (groupedError) {
        console.log("分组API失败，尝试使用简化API:", groupedError);

        // 检查是否是数据库硬件故障
        if (groupedError.data && groupedError.data.error_code === 'DATABASE_HARDWARE_ERROR') {
          wx.showModal({
            title: '数据库故障',
            content: '检测到数据库硬件故障，请联系系统管理员检查服务器硬盘状态。',
            showCancel: false,
            confirmText: '知道了'
          });
          this.setData({ loading: false });
          return;
        }
      }

      // 如果分组API失败，使用简化API作为备用方案
      try {
        console.log("尝试使用简化API加载反馈单数据");
        const simpleRes = await app.request({
          url: "/api/feedbacks/user/simple",
          method: "GET",
          data: {
            limit: this.data.pageSize,
            offset: 0
          }
        });

        if (simpleRes.data.success) {
          const feedbacks = simpleRes.data.data || [];
          console.log("简化API返回的反馈单数据:", feedbacks);

          // 手动分组数据
          const groupedFeedbacks = this.groupFeedbacksManually(feedbacks);
          console.log("手动分组后的数据:", groupedFeedbacks);

          // 计算总反馈数量
          const totalFeedbacks = feedbacks.length;

          // 准备要更新的数据
          const updateData = {
            groupedFeedbacks: groupedFeedbacks,
            filteredGroupedFeedbacks: groupedFeedbacks,
            totalFeedbackCount: totalFeedbacks,
            feedbacks: [],
            taskInfo: null,
            loading: false,
            currentPage: 1,
            hasMore: feedbacks.length === this.data.pageSize, // 如果返回的数据等于pageSize，可能还有更多
          };

          // 在首次加载或明确要求重置时重置展开状态
          if (this.data.isFirstLoad || resetExpandState) {
            updateData.expandedProjects = {};
            updateData.expandedTasks = {};

            // 默认展开第一个工程和第一个任务单，方便用户直接看到反馈单
            if (groupedFeedbacks.length > 0) {
              const firstProject = groupedFeedbacks[0];
              updateData.expandedProjects[firstProject.id] = true;

              if (firstProject.tasks && firstProject.tasks.length > 0) {
                const firstTask = firstProject.tasks[0];
                const taskKey = firstTask.id || firstTask.task_number;
                updateData.expandedTasks[taskKey] = true;
              }
            }
          }

          this.setData(updateData);

          // 更新页面标题
          wx.setNavigationBarTitle({
            title: `我的现场信息反馈记录 (${totalFeedbacks})`,
          });
          return; // 成功加载，直接返回
        }
      } catch (simpleError) {
        console.log("简化API也失败了:", simpleError);

        // 检查是否是数据库硬件故障
        if (simpleError.data && simpleError.data.error_code === 'DATABASE_HARDWARE_ERROR') {
          wx.showModal({
            title: '数据库故障',
            content: '检测到数据库硬件故障，请联系系统管理员检查服务器硬盘状态。',
            showCancel: false,
            confirmText: '知道了'
          });
          return;
        }

        throw simpleError; // 抛出错误，让外层catch处理
      }

      // 如果所有API都失败了，显示空状态
      this.setData({
        groupedFeedbacks: [],
        filteredGroupedFeedbacks: [],
        totalFeedbackCount: 0,
        feedbacks: [],
        taskInfo: null,
        loading: false,
      });

      wx.setNavigationBarTitle({
        title: `我的现场信息反馈记录 (0)`,
      });
    } catch (error) {
      console.error("加载现场信息反馈记录失败:", error);

      // 检查是否是数据库硬件故障
      if (error.data && error.data.error_code === 'DATABASE_HARDWARE_ERROR') {
        wx.showModal({
          title: '数据库故障',
          content: '检测到数据库硬件故障，请联系系统管理员检查服务器硬盘状态。',
          showCancel: false,
          confirmText: '知道了'
        });
      } else {
        wx.showToast({
          title: "网络错误",
          icon: "none",
        });
      }

      this.setData({ loading: false });
    }
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    const Formatter = require('../utils/formatter');
    return Formatter.formatDateTime(dateTimeStr, 'YYYY-MM-DD HH:mm');
  },

  // 计算总反馈数量
  calculateTotalFeedbackCount(groupedFeedbacks) {
    return groupedFeedbacks.reduce((total, project) =>
      total + project.tasks.reduce((taskTotal, task) => taskTotal + task.feedbacks.length, 0), 0);
  },

  // 点击现场信息反馈记录项
  onFeedbackTap(e) {
    const feedback = e.currentTarget.dataset.feedback;
    console.log('点击反馈单，准备跳转:', feedback);
    console.log('反馈单ID:', feedback.id);

    if (!feedback || !feedback.id) {
      wx.showToast({
        title: '反馈单数据错误',
        icon: 'none'
      });
      return;
    }

    const url = `/subpackages/feedback-management/feedback-detail/feedback-detail?id=${feedback.id}`;
    console.log('跳转URL:', url);

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error('页面跳转失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 切换工程展开状态
  onToggleProject(e) {
    const projectId = e.currentTarget.dataset.projectId;
    const expandedProjects = { ...this.data.expandedProjects };
    expandedProjects[projectId] = !expandedProjects[projectId];
    this.setData({ expandedProjects });
  },

  // 切换任务单展开状态
  onToggleTask(e) {
    const taskId = e.currentTarget.dataset.taskId;
    console.log('点击任务单展开/收起，任务单ID:', taskId, '类型:', typeof taskId);
    console.log('当前展开状态:', this.data.expandedTasks);

    // 检查taskId是否有效
    if (!taskId) {
      console.error('任务单ID为空或未定义');
      return;
    }

    const expandedTasks = { ...this.data.expandedTasks };
    expandedTasks[taskId] = !expandedTasks[taskId];

    console.log('更新后的展开状态:', expandedTasks);
    this.setData({ expandedTasks });
  },

  // 搜索输入
  onSearchInput(e) {
    const keyword = e.detail.value.trim();
    this.setData({ searchKeyword: keyword });
    this.filterFeedbacks(keyword);
  },

  // 手动分组反馈单数据（当新API不可用时的回退方案）
  groupFeedbacksManually(feedbacks) {
    const projects = {};

    feedbacks.forEach(feedback => {
      const projectKey = feedback.project_name || 'unknown';
      const taskKey = feedback.task_number || 'unknown';

      // 初始化工程
      if (!projects[projectKey]) {
        projects[projectKey] = {
          id: projectKey,
          name: feedback.project_name || '未知工程',
          code: '',
          construction_unit: feedback.construction_unit || '',
          tasks: {}
        };
      }

      // 初始化任务单
      if (!projects[projectKey].tasks[taskKey]) {
        projects[projectKey].tasks[taskKey] = {
          id: feedback.task_number, // 使用task_number作为id
          task_number: feedback.task_number || '未知任务单',
          part_name: feedback.part_name || '',
          strength_grade: '',
          scheduled_time: '',
          supply_status: '',
          feedbacks: []
        };
      }

      // 添加反馈单
      projects[projectKey].tasks[taskKey].feedbacks.push({
        ...feedback,
        feedback_time_text: this.formatDateTime(feedback.feedback_time)
      });
    });

    // 转换为数组格式
    return Object.values(projects).map(project => ({
      ...project,
      tasks: Object.values(project.tasks)
    }));
  },

  // 过滤反馈单
  filterFeedbacks(keyword) {
    if (!keyword) {
      const totalFeedbacks = this.calculateTotalFeedbackCount(this.data.groupedFeedbacks);
      this.setData({
        filteredGroupedFeedbacks: this.data.groupedFeedbacks,
        totalFeedbackCount: totalFeedbacks
      });
      return;
    }

    const filtered = this.data.groupedFeedbacks.map(project => {
      // 检查工程名称是否匹配
      const projectMatches = project.name.toLowerCase().includes(keyword.toLowerCase()) ||
                            (project.construction_unit && project.construction_unit.toLowerCase().includes(keyword.toLowerCase()));

      const filteredTasks = project.tasks.map(task => {
        // 检查任务单信息是否匹配
        const taskMatches = task.task_number.toLowerCase().includes(keyword.toLowerCase()) ||
                           (task.part_name && task.part_name.toLowerCase().includes(keyword.toLowerCase()));

        // 检查反馈单内容是否匹配
        const filteredFeedbacks = task.feedbacks.filter(feedback => {
          return feedback.notes && feedback.notes.toLowerCase().includes(keyword.toLowerCase()) ||
                 feedback.category && feedback.category.toLowerCase().includes(keyword.toLowerCase()) ||
                 feedback.feedback_user_name && feedback.feedback_user_name.toLowerCase().includes(keyword.toLowerCase());
        });

        // 如果工程或任务单匹配，显示所有反馈单；否则只显示匹配的反馈单
        return {
          ...task,
          feedbacks: (projectMatches || taskMatches) ? task.feedbacks : filteredFeedbacks
        };
      }).filter(task => {
        // 只保留有反馈单的任务单，或者任务单本身匹配的
        return task.feedbacks.length > 0 ||
               task.task_number.toLowerCase().includes(keyword.toLowerCase()) ||
               (task.part_name && task.part_name.toLowerCase().includes(keyword.toLowerCase()));
      });

      return {
        ...project,
        tasks: filteredTasks
      };
    }).filter(project => {
      // 只保留有任务单的工程，或者工程本身匹配的
      return project.tasks.length > 0 ||
             project.name.toLowerCase().includes(keyword.toLowerCase()) ||
             (project.construction_unit && project.construction_unit.toLowerCase().includes(keyword.toLowerCase()));
    });

    // 计算过滤后的总反馈数量
    const totalFilteredFeedbacks = this.calculateTotalFeedbackCount(filtered);

    this.setData({
      filteredGroupedFeedbacks: filtered,
      totalFeedbackCount: totalFilteredFeedbacks
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    // 重置分页状态
    this.setData({
      currentPage: 1,
      hasMore: true
    });

    if (this.data.taskId) {
      this.loadFeedbacks(this.data.taskId);
    } else {
      this.loadAllFeedbacks(true);
    }
    wx.stopPullDownRefresh();
  },

  // 滚动到底部加载更多
  onReachBottom() {
    if (!this.data.hasMore || this.data.isLoadingMore || this.data.taskId) {
      return; // 如果没有更多数据、正在加载或者是特定任务单页面，则不加载
    }

    this.loadMoreFeedbacks();
  },

  // 加载更多反馈单
  async loadMoreFeedbacks() {
    if (this.data.isLoadingMore || !this.data.hasMore) {
      return;
    }

    try {
      this.setData({ isLoadingMore: true });
      const app = getApp();
      const nextPage = this.data.currentPage + 1;
      const offset = (nextPage - 1) * this.data.pageSize;

      console.log(`加载更多反馈单 - 页码: ${nextPage}, 偏移: ${offset}`);

      const res = await app.request({
        url: "/api/feedbacks/user/simple",
        method: "GET",
        data: {
          limit: this.data.pageSize,
          offset: offset
        }
      });

      if (res.data.success) {
        const newFeedbacks = res.data.data || [];
        console.log(`加载到 ${newFeedbacks.length} 条新反馈单`);

        if (newFeedbacks.length === 0) {
          // 没有更多数据了
          this.setData({
            hasMore: false,
            isLoadingMore: false
          });
          return;
        }

        // 手动分组新数据
        const newGroupedFeedbacks = this.groupFeedbacksManually(newFeedbacks);

        // 合并到现有数据中
        const existingGrouped = this.data.groupedFeedbacks;
        const mergedGrouped = this.mergeGroupedFeedbacks(existingGrouped, newGroupedFeedbacks);

        this.setData({
          groupedFeedbacks: mergedGrouped,
          filteredGroupedFeedbacks: mergedGrouped,
          currentPage: nextPage,
          totalFeedbackCount: this.data.totalFeedbackCount + newFeedbacks.length,
          isLoadingMore: false,
          hasMore: newFeedbacks.length === this.data.pageSize // 如果返回的数据少于pageSize，说明没有更多了
        });

        // 更新页面标题
        wx.setNavigationBarTitle({
          title: `我的现场信息反馈记录 (${this.data.totalFeedbackCount})`,
        });
      } else {
        this.setData({ isLoadingMore: false });
        wx.showToast({
          title: res.data.message || "加载失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("加载更多反馈单失败:", error);
      this.setData({ isLoadingMore: false });
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
    }
  },

  // 合并分组数据
  mergeGroupedFeedbacks(existing, newData) {
    const merged = [...existing];

    newData.forEach(newProject => {
      // 查找是否已存在相同的工程
      const existingProjectIndex = merged.findIndex(p => p.id === newProject.id);

      if (existingProjectIndex >= 0) {
        // 工程已存在，合并任务单
        const existingProject = merged[existingProjectIndex];

        newProject.tasks.forEach(newTask => {
          // 查找是否已存在相同的任务单
          const existingTaskIndex = existingProject.tasks.findIndex(t => t.id === newTask.id);

          if (existingTaskIndex >= 0) {
            // 任务单已存在，合并反馈单
            existingProject.tasks[existingTaskIndex].feedbacks = [
              ...existingProject.tasks[existingTaskIndex].feedbacks,
              ...newTask.feedbacks
            ];
          } else {
            // 任务单不存在，直接添加
            existingProject.tasks.push(newTask);
          }
        });
      } else {
        // 工程不存在，直接添加
        merged.push(newProject);
      }
    });

    return merged;
  },



  // 为特定任务单创建反馈记录
  onCreateFeedbackForTask(e) {
    const taskId = e.currentTarget.dataset.taskId;
    wx.navigateTo({
      url: `/subpackages/feedback-management/feedback/feedback?taskId=${taskId}`,
    });
  },


});